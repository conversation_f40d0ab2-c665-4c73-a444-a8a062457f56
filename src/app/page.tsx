'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Play, Pause, SkipForward, Ski<PERSON><PERSON><PERSON>, RotateCcw, Settings } from 'lucide-react';
import CSVImporter from '@/components/CSVImporter';
import TradingChart from '@/components/TradingChart';
import OrderPanel from '@/components/OrderPanel';
import PerformancePanel from '@/components/PerformancePanel';
import TradingHeader from '@/components/TradingHeader';
import {
  TradingSession,
  Order,
  Position,
  OrderType,
  CSVParseResult,
  Timeframe,
  UpdateMode,
  TickData,
  CandleData,
  BidAskPrice,
  ChartOrderPlacement
} from '@/types/trading';
import {
  calculateCommission,
  updateAccount,
  canExecuteOrder,
  formatCurrency,
  formatPrice,
  getBidAskFromTick,
  validateOrderPrice,
  shouldTriggerPendingOrder,
  getBidAskFromCandle,
  getExecutionPrice,
  checkStopLossTakeProfitBidAsk,
  calculateUnrealizedPnLBidAsk
} from '@/utils/tradingCalculations';
import { TimeframeAggregator } from '@/utils/timeframeAggregator';
import TimeframeSelector from '@/components/TimeframeSelector';
import UpdateModeSelector from '@/components/UpdateModeSelector';

const INITIAL_BALANCE = 10000;

export default function Home() {
  const [session, setSession] = useState<TradingSession>({
    symbol: '',
    data: [],
    baseData: [],
    dataType: 'candle',
    currentIndex: 0,
    isPlaying: false,
    playbackSpeed: 1000,
    orders: [],
    positions: [],
    account: {
      balance: INITIAL_BALANCE,
      equity: INITIAL_BALANCE,
      margin: 0,
      freeMargin: INITIAL_BALANCE,
      marginLevel: 0,
      totalPnL: 0,
      totalCommission: 0
    },
    startBalance: INITIAL_BALANCE,
    timeframe: 'M1',
    updateMode: 'complete',
    currentBid: 0,
    currentAsk: 0,
    spread: 0,
    lastKnownBid: 0,
    lastKnownAsk: 0
  });

  const [showImporter, setShowImporter] = useState(true);
  const [intraCandleStep, setIntraCandleStep] = useState(0);
  const [intraCandleSteps, setIntraCandleSteps] = useState(1);

  const currentCandle = session.data[session.currentIndex];
  const currentTick = session.tickData?.[session.currentIndex];

  // Helper function to get timeframe in seconds
  const getTimeframeSeconds = useCallback((timeframe: string): number => {
    switch (timeframe) {
      case 'S1': return 1;
      case 'S5': return 5;
      case 'S10': return 10;
      case 'S15': return 15;
      case 'S30': return 30;
      case 'M1': return 60;
      case 'M5': return 300;
      case 'M15': return 900;
      case 'M30': return 1800;
      case 'H1': return 3600;
      case 'H4': return 14400;
      case 'D1': return 86400;
      case 'W1': return 604800;
      case 'MN1': return 2592000;
      default: return 60; // Default to 1 minute
    }
  }, []);

  // Helper function to get ticks within a candle's timeframe
  const getTicksInCandle = useCallback((tickData: TickData[], candle: CandleData, timeframe: Timeframe): TickData[] => {
    const timeframeSeconds = getTimeframeSeconds(timeframe);
    const timeframeMs = timeframeSeconds * 1000;
    const candleStartTime = candle.timestamp;
    const candleEndTime = candleStartTime + timeframeMs;

    return tickData.filter(tick => {
      const tickTime = new Date(`${tick.date} ${tick.time}`).getTime();
      return tickTime >= candleStartTime && tickTime < candleEndTime;
    });
  }, [getTimeframeSeconds]);

  // Helper function to calculate average spread from ticks within a candle
  const calculateAverageSpread = useCallback((ticks: TickData[]): number => {
    if (ticks.length === 0) return 0.00002; // Default spread fallback

    const validSpreads: number[] = [];

    ticks.forEach(tick => {
      // Only include ticks that have both bid and ask prices
      if (tick.bid !== undefined && tick.ask !== undefined && tick.bid > 0 && tick.ask > 0) {
        const spread = tick.ask - tick.bid;
        // Validate spread is reasonable (positive and not too large)
        if (spread > 0 && spread < 0.01) { // Max 100 pips spread filter
          validSpreads.push(spread);
        }
      }
    });

    if (validSpreads.length === 0) {
      return 0.00002; // Default spread if no valid spreads found
    }

    // Calculate average spread
    const averageSpread = validSpreads.reduce((sum, spread) => sum + spread, 0) / validSpreads.length;
    return averageSpread;
  }, []);

  // Memoized display data to prevent unnecessary re-renders
  const displayData = useMemo(() => {
    // For tick data in complete mode, we need to adjust the current candle close to match bid price
    if (session.dataType === 'tick' && session.updateMode === 'complete' && session.tickData) {
      const adjustedData = [...session.data];

      if (session.currentIndex < adjustedData.length) {
        const currentCandle = adjustedData[session.currentIndex];

        if (currentCandle) {
          // Find the last tick within this candle's time range
          const ticksInCandle = getTicksInCandle(session.tickData, currentCandle, session.timeframe);

          if (ticksInCandle.length > 0) {
            // Get the last tick in this candle for bid price
            const lastTickInCandle = ticksInCandle[ticksInCandle.length - 1];
            const bidAsk = getBidAskFromTick(lastTickInCandle, session.lastKnownBid, session.lastKnownAsk);

            if (bidAsk && bidAsk.bid > 0) {
              // Update current candle close to match bid price from last tick
              adjustedData[session.currentIndex] = {
                ...currentCandle,
                close: bidAsk.bid,
                // Ensure high/low accommodate the new close price
                high: Math.max(currentCandle.high, bidAsk.bid),
                low: Math.min(currentCandle.low, bidAsk.bid)
              };
            }
          }
        }
      }

      return adjustedData;
    }

    if (session.dataType !== 'tick' || !session.tickData) {
      return session.data; // Return original data for non-tick data
    }

    if (session.updateMode === 'complete') {
      // This case is already handled above for tick data in complete mode
      return session.data;
    }

    // Clone the data array
    const intraCandleData = [...session.data];

    if (session.currentIndex >= intraCandleData.length) {
      return intraCandleData;
    }

    const currentCandle = intraCandleData[session.currentIndex];
    if (!currentCandle) {
      return intraCandleData;
    }

    // Get ticks for current candle
    const ticksInCandle = getTicksInCandle(session.tickData, currentCandle, session.timeframe);

    if (ticksInCandle.length === 0) {
      return intraCandleData;
    }

    // Get ticks up to current step
    const ticksUpToStep = ticksInCandle.slice(0, Math.min(intraCandleStep + 1, ticksInCandle.length));

    if (ticksUpToStep.length === 0) {
      return intraCandleData;
    }

    // Calculate OHLC from ticks up to current step
    const prices = ticksUpToStep.map(tick => {
      if (tick.bid !== undefined && tick.ask !== undefined) {
        return (tick.bid + tick.ask) / 2;
      }
      return tick.bid || tick.ask || tick.last || 0;
    }).filter(price => price > 0 && !isNaN(price) && isFinite(price));

    if (prices.length === 0) {
      return intraCandleData;
    }

    const open = prices[0];

    // For close price, prioritize current bid price from the last tick
    const lastTick = ticksUpToStep[ticksUpToStep.length - 1];
    let close: number;

    // Get bid/ask from last tick with fallback
    const lastTickBidAsk = getBidAskFromTick(lastTick, session.lastKnownBid, session.lastKnownAsk);
    if (lastTickBidAsk && lastTickBidAsk.bid > 0) {
      // Use current bid price as candle close
      close = lastTickBidAsk.bid;
    } else {
      // Fallback to calculated price from mid
      close = prices[prices.length - 1];
    }

    const high = Math.max(...prices, close); // Include close in high calculation
    const low = Math.min(...prices, close);  // Include close in low calculation
    const volume = ticksUpToStep.reduce((sum, tick) => sum + (tick.volume || 0), 0);

    // Validate OHLC values before updating
    if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close) ||
        !isFinite(open) || !isFinite(high) || !isFinite(low) || !isFinite(close)) {
      return intraCandleData;
    }

    // Update the current candle with partial data
    intraCandleData[session.currentIndex] = {
      ...currentCandle,
      open,
      high,
      low,
      close,
      vol: volume,
      tickVol: ticksUpToStep.length
    };

    return intraCandleData;
  }, [session.dataType, session.updateMode, session.tickData, session.data, session.currentIndex, session.timeframe, intraCandleStep, session.lastKnownBid, session.lastKnownAsk, getTicksInCandle, calculateAverageSpread]);

  // Calculate progress based on data type
  const totalItems = session.dataType === 'tick'
    ? (session.tickData?.length || 0)
    : session.data.length;
  const progress = totalItems > 0 ? (session.currentIndex / (totalItems - 1)) * 100 : 0;

  // Get current bid/ask prices for the current step
  const getCurrentBidAsk = useCallback((): BidAskPrice | null => {
    // For tick data
    if (session.dataType === 'tick' && session.tickData) {
      if (session.updateMode === 'complete') {
        // Complete mode: find the last tick within current candle's time range
        if (session.currentIndex >= session.data.length) return null;

        const currentCandle = session.data[session.currentIndex];
        if (!currentCandle) return null;

        // Find all ticks within this candle's time range
        const ticksInCandle = getTicksInCandle(session.tickData, currentCandle, session.timeframe);

        if (ticksInCandle.length === 0) return null;

        // Get the last tick in this candle for bid price
        const lastTickInCandle = ticksInCandle[ticksInCandle.length - 1];

        // Calculate average spread from all ticks in this candle
        const averageSpread = calculateAverageSpread(ticksInCandle);

        // Get bid price from last tick (with fallback)
        const lastTickBidAsk = getBidAskFromTick(lastTickInCandle, session.lastKnownBid, session.lastKnownAsk);
        if (!lastTickBidAsk) return null;

        // Use last tick's bid price + average spread for ask price
        const bid = lastTickBidAsk.bid;
        const ask = bid + averageSpread;
        const mid = (bid + ask) / 2;

        return {
          bid,
          ask,
          spread: averageSpread,
          timestamp: lastTickInCandle.timestamp,
          mid
        };
      } else {
        // Intra-candle mode: use tick-by-tick progression within current candle
        if (session.currentIndex >= session.data.length) return null;

        const currentCandle = session.data[session.currentIndex];
        if (!currentCandle) return null;

        // Find all ticks within this candle's time range
        const ticksInCandle = getTicksInCandle(session.tickData, currentCandle, session.timeframe);

        if (ticksInCandle.length === 0) return null;

        const tickIndex = Math.min(intraCandleStep, ticksInCandle.length - 1);
        const tick = ticksInCandle[tickIndex];

        return getBidAskFromTick(tick, session.lastKnownBid, session.lastKnownAsk);
      }
    }

    // For candle data
    if (!currentCandle) return null;

    if (session.updateMode === 'complete' || session.timeframe === 'M1') {
      // In complete mode, for tick-derived candles treat close as bid, for regular candles treat as mid
      const treatCloseAsBid = session.dataType === 'tick';
      return getBidAskFromCandle(currentCandle, undefined, treatCloseAsBid);
    }

    // Intra-candle mode: calculate current step price
    let currentPrice = currentCandle.open;
    
    if (intraCandleStep > 0) {
      // Calculate progress through the candle
      const progress = intraCandleStep / (intraCandleSteps - 1);

      // Get M1 data for this timeframe period if available
      const timeframeSeconds = getTimeframeSeconds(session.timeframe);
      const candleStartTime = currentCandle.timestamp;
      const candleEndTime = candleStartTime + (timeframeSeconds * 1000);

      const intraCandleM1Data = session.baseData.filter(m1Candle =>
        m1Candle.timestamp >= candleStartTime &&
        m1Candle.timestamp < candleEndTime
      );

      if (intraCandleM1Data.length > 0) {
        // Use actual M1 data
        const stepsToInclude = Math.ceil(intraCandleM1Data.length * progress);
        const includedData = intraCandleM1Data.slice(0, stepsToInclude);
        currentPrice = includedData.length > 0 ? includedData[includedData.length - 1].close : currentCandle.open;
      } else {
        // Fallback to interpolation
        currentPrice = currentCandle.open + (currentCandle.close - currentCandle.open) * progress;
      }
    }

    return getBidAskFromCandle(currentCandle, currentPrice);
  }, [currentCandle, session.updateMode, session.timeframe, session.dataType, session.tickData, session.currentIndex, intraCandleStep, intraCandleSteps, session.baseData, session.lastKnownBid, session.lastKnownAsk, getTicksInCandle, calculateAverageSpread]);

  // Helper function to get current step price (for backward compatibility)
  const getCurrentStepPrice = useCallback((): number => {
    const bidAsk = getCurrentBidAsk();
    return bidAsk?.mid || bidAsk?.ask || bidAsk?.bid || 0;
  }, [getCurrentBidAsk]);

  // Helper function to calculate realtime delay for tick data
  const calculateRealtimeDelay = useCallback((): number => {
    if (session.dataType !== 'tick' || !session.tickData || session.playbackSpeed !== 'realtime') {
      return typeof session.playbackSpeed === 'number' ? session.playbackSpeed : 1000;
    }

    // For tick data in realtime mode, calculate actual time difference
    if (session.updateMode === 'complete') {
      // Complete mode: use time between current and next tick
      const currentTickIndex = session.currentIndex;
      const nextTickIndex = currentTickIndex + 1;

      if (nextTickIndex >= session.tickData.length) {
        return 1000; // Default if no next tick
      }

      const currentTick = session.tickData[currentTickIndex];
      const nextTick = session.tickData[nextTickIndex];

      if (!currentTick || !nextTick) {
        return 1000; // Default if ticks not found
      }

      const currentTime = new Date(`${currentTick.date} ${currentTick.time}`).getTime();
      const nextTime = new Date(`${nextTick.date} ${nextTick.time}`).getTime();

      return Math.max(1, nextTime - currentTime); // At least 1ms delay
    } else {
      // Intra-candle mode: use time between ticks within current candle
      if (session.currentIndex >= session.data.length || !session.tickData) {
        return 1000;
      }

      const currentCandle = session.data[session.currentIndex];
      if (!currentCandle) {
        return 1000;
      }

      // Find all ticks within this candle's time range
      const ticksInCandle = getTicksInCandle(session.tickData, currentCandle, session.timeframe);

      if (ticksInCandle.length === 0 || intraCandleStep >= ticksInCandle.length - 1) {
        return 1000; // Default if no ticks or at end
      }

      const currentTick = ticksInCandle[intraCandleStep];
      const nextTick = ticksInCandle[intraCandleStep + 1];

      if (!currentTick || !nextTick) {
        return 1000;
      }

      const currentTime = new Date(`${currentTick.date} ${currentTick.time}`).getTime();
      const nextTime = new Date(`${nextTick.date} ${nextTick.time}`).getTime();

      return Math.max(1, nextTime - currentTime); // At least 1ms delay
    }
  }, [session.dataType, session.tickData, session.playbackSpeed, session.updateMode, session.currentIndex, session.data, session.timeframe, intraCandleStep, getTicksInCandle]);

  // Auto-play functionality with intra-candle support
  useEffect(() => {
    if (!session.isPlaying) {
      return;
    }

    // Calculate if we can advance
    const canAdvance = session.dataType === 'tick'
      ? session.updateMode === 'complete'
        ? session.currentIndex < (session.tickData?.length || 0) - 1
        : session.currentIndex < session.data.length - 1 || intraCandleStep < intraCandleSteps - 1
      : session.updateMode === 'complete' || session.timeframe === 'M1'
      ? session.currentIndex < session.data.length - 1
      : session.currentIndex < session.data.length - 1 || intraCandleStep < intraCandleSteps - 1;

    if (!canAdvance) {
      return;
    }

    // Calculate delay (either fixed speed or realtime)
    const delay = calculateRealtimeDelay();

    const timer = setTimeout(() => {
      setSession(prev => {
        if (prev.dataType === 'tick') {
          if (prev.updateMode === 'complete') {
            // Tick mode complete: advance to next tick
            const maxIndex = (prev.tickData?.length || 0) - 1;
            return {
              ...prev,
              currentIndex: Math.min(prev.currentIndex + 1, maxIndex)
            };
          } else {
            // Tick mode intra-candle: advance step within current candle or move to next candle
            if (intraCandleStep < intraCandleSteps - 1) {
              // Stay on same candle, advance intra-candle step
              setIntraCandleStep(intraCandleStep + 1);
              return prev; // Don't change currentIndex
            } else {
              // Move to next candle and reset intra-candle step
              setIntraCandleStep(0);
              return {
                ...prev,
                currentIndex: Math.min(prev.currentIndex + 1, prev.data.length - 1)
              };
            }
          }
        } else if (prev.updateMode === 'complete' || prev.timeframe === 'M1') {
          // Standard mode: advance to next candle
          return {
            ...prev,
            currentIndex: Math.min(prev.currentIndex + 1, prev.data.length - 1)
          };
        } else {
          // Intra-candle mode: advance step within current candle or move to next candle
          if (intraCandleStep < intraCandleSteps - 1) {
            // Stay on same candle, advance intra-candle step
            setIntraCandleStep(intraCandleStep + 1);
            return prev; // Don't change currentIndex
          } else {
            // Move to next candle and reset intra-candle step
            setIntraCandleStep(0);
            return {
              ...prev,
              currentIndex: Math.min(prev.currentIndex + 1, prev.data.length - 1)
            };
          }
        }
      });
    }, delay);

    return () => clearTimeout(timer);
  }, [session.isPlaying, session.currentIndex, session.playbackSpeed, session.data.length, session.updateMode, session.timeframe, intraCandleStep, intraCandleSteps]);

  // Dedicated bid/ask price update effect - runs frequently for visual synchronization
  useEffect(() => {
    // Check bounds for both candle and tick data
    const maxIndex = session.dataType === 'tick'
      ? (session.tickData?.length || 0)
      : session.data.length;

    if (maxIndex === 0 || session.currentIndex >= maxIndex) return;

    const currentBidAsk = getCurrentBidAsk();
    if (!currentBidAsk) return;

    // Update only bid/ask prices for visual synchronization
    setSession(prev => ({
      ...prev,
      currentBid: currentBidAsk.bid,
      currentAsk: currentBidAsk.ask,
      spread: currentBidAsk.spread,
      // Update last known values when we have valid bid/ask prices
      lastKnownBid: currentBidAsk.bid > 0 ? currentBidAsk.bid : prev.lastKnownBid,
      lastKnownAsk: currentBidAsk.ask > 0 ? currentBidAsk.ask : prev.lastKnownAsk
    }));
  }, [session.currentIndex, session.dataType, session.updateMode, session.timeframe, intraCandleStep, session.data.length]);

  // Update positions and check stop loss/take profit using bid/ask prices
  useEffect(() => {
    // Check bounds for both candle and tick data
    const maxIndex = session.dataType === 'tick'
      ? (session.tickData?.length || 0)
      : session.data.length;

    if (maxIndex === 0 || session.currentIndex >= maxIndex) return;

    const currentCandle = session.data[session.currentIndex];
    const currentBidAsk = getCurrentBidAsk();
    
    if (!currentBidAsk) return;

    setSession(prev => {
      const updatedPositions: Position[] = [];
      const updatedOrders = [...prev.orders];

      // Check pending orders for trigger conditions
      updatedOrders.forEach((order, index) => {
        if (order.status === 'pending' && order.orderExecutionType !== 'market') {
          const triggerCheck = shouldTriggerPendingOrder(order, currentBidAsk);

          if (triggerCheck.shouldTrigger) {
            // Execute the pending order
            const executionPrice = triggerCheck.executionPrice || order.executionPrice || order.entryPrice;

            // Check if order can still be executed (margin requirements)
            const canExecute = canExecuteOrder(session.account, order.type, order.size, executionPrice);
            if (canExecute.canExecute) {
              const commission = calculateCommission(order.size);

              // Get current timestamp (from candle or tick)
              const currentTimestamp = session.dataType === 'tick' && session.tickData
                ? session.tickData[session.currentIndex]?.timestamp || Date.now()
                : currentCandle?.timestamp || Date.now();

              // Update order to filled status
              updatedOrders[index] = {
                ...order,
                status: 'filled',
                entryPrice: executionPrice, // Update with actual execution price
                commission,
                timestamp: currentTimestamp, // Update execution timestamp
                // Add chart index for accurate marker positioning
                ...(session.currentIndex >= 0 && { chartIndex: session.currentIndex })
              } as Order & { chartIndex?: number };

              // Create new position
              const currentMarketPrice = order.type === 'buy' ? currentBidAsk.bid : currentBidAsk.ask;

              const newPosition: Position = {
                id: `pos_${order.id}`,
                orderId: order.id,
                type: order.type,
                symbol: order.symbol,
                size: order.size,
                entryPrice: executionPrice,
                currentPrice: currentMarketPrice,
                stopLoss: order.stopLoss,
                takeProfit: order.takeProfit,
                entryTimestamp: currentTimestamp,
                unrealizedPnL: 0, // Will be calculated below
                commission
              };

              // Calculate initial unrealized P&L
              newPosition.unrealizedPnL = calculateUnrealizedPnLBidAsk(newPosition, currentBidAsk);
              updatedPositions.push(newPosition);
            }
          }
        }
      });

      // Update positions and check for stop loss/take profit
      prev.positions.forEach(position => {
        const slTpCheck = checkStopLossTakeProfitBidAsk(position, currentBidAsk);

        if (slTpCheck.shouldClose) {
          // Close position using appropriate bid/ask price
          const exitPrice = slTpCheck.exitPrice || getExecutionPrice(
            position.type === 'buy' ? 'sell' : 'buy', 
            currentBidAsk
          );

          const commission = calculateCommission(position.size);
          const pnl = calculateUnrealizedPnLBidAsk(position, currentBidAsk) - commission;

          // Get current timestamp (from candle or tick)
          const currentTimestamp = session.dataType === 'tick' && session.tickData
            ? session.tickData[session.currentIndex]?.timestamp || Date.now()
            : currentCandle?.timestamp || Date.now();

          // Update the original order
          const orderIndex = updatedOrders.findIndex(o => o.id === position.orderId);
          if (orderIndex !== -1) {
            updatedOrders[orderIndex] = {
              ...updatedOrders[orderIndex],
              status: 'closed',
              exitPrice,
              exitTimestamp: currentTimestamp,
              pnl,
              commission,
              // Add exit chart index for accurate marker positioning
              ...(session.currentIndex >= 0 && { exitChartIndex: session.currentIndex })
            } as Order & { exitChartIndex?: number };
          }
        } else {
          // Update position with current bid/ask
          const currentMarketPrice = position.type === 'buy' ? currentBidAsk.bid : currentBidAsk.ask;
          updatedPositions.push({
            ...position,
            currentPrice: currentMarketPrice,
            unrealizedPnL: calculateUnrealizedPnLBidAsk(position, currentBidAsk)
          });
        }
      });

      // Update account
      const updatedAccount = updateAccount(
        { ...prev.account, balance: prev.startBalance },
        updatedPositions,
        updatedOrders
      );

      // Update trading state (bid/ask prices are updated separately for better synchronization)
      return {
        ...prev,
        positions: updatedPositions,
        orders: updatedOrders,
        account: updatedAccount
      };
    });
  }, [session.currentIndex, session.data]);

  // Helper function to convert ticks to candles
  const convertTicksToCandles = useCallback((ticks: TickData[], timeframeMinutes: number): CandleData[] => {
    if (!ticks.length) return [];

    const candles: CandleData[] = [];
    const timeframeMs = timeframeMinutes * 60 * 1000;

    // Group ticks by timeframe
    const tickGroups: { [key: string]: TickData[] } = {};

    ticks.forEach(tick => {
      const tickTime = new Date(`${tick.date} ${tick.time}`).getTime();
      const candleStartTime = Math.floor(tickTime / timeframeMs) * timeframeMs;
      const candleKey = candleStartTime.toString();

      if (!tickGroups[candleKey]) {
        tickGroups[candleKey] = [];
      }
      tickGroups[candleKey].push(tick);
    });

    // Convert each group to a candle
    Object.keys(tickGroups).sort().forEach(candleKey => {
      const groupTicks = tickGroups[candleKey];
      if (groupTicks.length === 0) return;

      const prices = groupTicks.map(tick => {
        // Use mid price if both bid and ask available, otherwise use available price
        if (tick.bid !== undefined && tick.ask !== undefined) {
          return (tick.bid + tick.ask) / 2;
        }
        return tick.bid || tick.ask || tick.last || 0;
      }).filter(price => price > 0 && !isNaN(price) && isFinite(price));

      if (prices.length === 0) return;

      const candleTime = new Date(parseInt(candleKey));
      const open = prices[0];

      // For close price, prioritize bid price from the last tick
      const lastTick = groupTicks[groupTicks.length - 1];
      let close: number;

      if (lastTick.bid !== undefined && lastTick.bid > 0) {
        // Use bid price as candle close
        close = lastTick.bid;
      } else {
        // Fallback to calculated mid price
        close = prices[prices.length - 1];
      }

      const high = Math.max(...prices, close); // Include close in high calculation
      const low = Math.min(...prices, close);  // Include close in low calculation
      const volume = groupTicks.reduce((sum, tick) => sum + (tick.volume || 0), 0);

      // Validate OHLC values before adding candle
      if (isNaN(open) || isNaN(high) || isNaN(low) || isNaN(close) ||
          !isFinite(open) || !isFinite(high) || !isFinite(low) || !isFinite(close)) {
        return;
      }

      candles.push({
        date: candleTime.toISOString().split('T')[0].replace(/-/g, '.'),
        time: candleTime.toTimeString().split(' ')[0],
        open,
        high,
        low,
        close,
        tickVol: groupTicks.length,
        vol: volume,
        spread: 0,
        timestamp: parseInt(candleKey)
      });
    });

    return candles;
  }, []);

  const handleDataLoaded = useCallback((result: CSVParseResult) => {
    if (result.data?.length || result.tickData?.length) {
      let candleData: CandleData[] = [];
      let baseData: CandleData[] = [];

      if (result.dataType === 'tick' && result.tickData) {
        // Convert tick data to candles for chart display
        candleData = convertTicksToCandles(result.tickData, 1/60); // Start with S1 candles (1 second = 1/60 minute)
        baseData = candleData; // Use converted candles as base data
      } else if (result.data) {
        // Use provided candle data
        candleData = result.data;
        baseData = result.data;
      }

      // Initialize bid/ask from first data point
      let initialBid = 0;
      let initialAsk = 0;
      let initialSpread = 0;

      if (result.dataType === 'tick' && result.tickData && result.tickData.length > 0) {
        const firstTick = result.tickData[0];
        const bidAsk = getBidAskFromTick(firstTick, 0, 0); // No fallback for first tick
        if (bidAsk) {
          initialBid = bidAsk.bid;
          initialAsk = bidAsk.ask;
          initialSpread = bidAsk.spread;
        }
      } else if (candleData.length > 0) {
        const firstCandle = candleData[0];
        // For tick-derived candles treat close as bid, for regular candles treat as mid
        const treatCloseAsBid = result.dataType === 'tick';
        const bidAsk = getBidAskFromCandle(firstCandle, undefined, treatCloseAsBid);
        initialBid = bidAsk.bid;
        initialAsk = bidAsk.ask;
        initialSpread = bidAsk.spread;
      }

      setSession(prev => ({
        ...prev,
        symbol: result.symbol || 'Unknown',
        data: candleData, // Chart will show candles
        baseData: baseData, // Store base data for timeframe switching
        tickData: result.tickData, // Keep original tick data for granular updates
        dataType: result.dataType,
        currentIndex: 0,
        isPlaying: false,
        playbackSpeed: result.dataType === 'tick' ? 100 : 1000, // Faster for ticks
        orders: [],
        positions: [],
        account: {
          balance: INITIAL_BALANCE,
          equity: INITIAL_BALANCE,
          margin: 0,
          freeMargin: INITIAL_BALANCE,
          marginLevel: 0,
          totalPnL: 0,
          totalCommission: 0
        },
        startBalance: INITIAL_BALANCE,
        timeframe: result.dataType === 'tick' ? 'S1' : 'M1', // Start with S1 for tick data, M1 for candle data
        updateMode: 'complete', // Start with complete mode
        precision: result.precision, // Add auto-detected precision
        currentBid: initialBid,
        currentAsk: initialAsk,
        spread: initialSpread,
        lastKnownBid: initialBid,
        lastKnownAsk: initialAsk
      }));
      setShowImporter(false);
    }
  }, [convertTicksToCandles]);

  const handleClosePosition = useCallback((positionId: string) => {
    // Check bounds for both candle and tick data
    const maxIndex = session.dataType === 'tick'
      ? (session.tickData?.length || 0)
      : session.data.length;

    if (maxIndex === 0 || session.currentIndex >= maxIndex) return;

    const currentCandle = session.data[session.currentIndex];
    const currentBidAsk = getCurrentBidAsk();
    
    if (!currentBidAsk) return;

    setSession(prev => {
      const position = prev.positions.find(p => p.id === positionId);
      if (!position) return prev;

      // Use appropriate bid/ask price for closing
      const exitPrice = getExecutionPrice(
        position.type === 'buy' ? 'sell' : 'buy', 
        currentBidAsk
      );

      const commission = calculateCommission(position.size);
      const pnl = calculateUnrealizedPnLBidAsk(position, currentBidAsk) - commission;

      // Get current timestamp (from candle or tick)
      const currentTimestamp = prev.dataType === 'tick' && prev.tickData
        ? prev.tickData[prev.currentIndex]?.timestamp || Date.now()
        : currentCandle?.timestamp || Date.now();

      // Update the original order
      const updatedOrders = prev.orders.map(order =>
        order.id === position.orderId
          ? {
              ...order,
              status: 'closed' as const,
              exitPrice,
              exitTimestamp: currentTimestamp,
              pnl,
              commission,
              // Add exit chart index for accurate marker positioning
              ...(prev.currentIndex >= 0 && { exitChartIndex: prev.currentIndex })
            } as Order & { exitChartIndex?: number }
          : order
      );

      // Remove position from open positions
      const updatedPositions = prev.positions.filter(p => p.id !== positionId);

      return {
        ...prev,
        orders: updatedOrders,
        positions: updatedPositions
      };
    });
  }, [session.data, session.currentIndex, getCurrentBidAsk]);

  const handlePlaceOrder = useCallback((orderData: {
    type: OrderType;
    size: number;
    stopLoss?: number;
    takeProfit?: number;
  }) => {
    // Check bounds for both candle and tick data
    const maxIndex = session.dataType === 'tick'
      ? (session.tickData?.length || 0)
      : session.data.length;

    if (maxIndex === 0 || session.currentIndex >= maxIndex) return;

    const currentCandle = session.data[session.currentIndex];
    const currentBidAsk = getCurrentBidAsk();
    
    if (!currentBidAsk) return;

    // Use appropriate bid/ask price for order execution
    const executionPrice = getExecutionPrice(orderData.type, currentBidAsk);

    // Check if order can be executed
    const canExecute = canExecuteOrder(session.account, orderData.type, orderData.size, executionPrice);
    if (!canExecute.canExecute) {
      alert(canExecute.reason);
      return;
    }

    const orderId = `order_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const commission = calculateCommission(orderData.size);

    // Get current timestamp (from candle or tick)
    const currentTimestamp = session.dataType === 'tick' && session.tickData
      ? session.tickData[session.currentIndex]?.timestamp || Date.now()
      : currentCandle?.timestamp || Date.now();

    const newOrder: Order = {
      id: orderId,
      type: orderData.type,
      symbol: session.symbol,
      size: orderData.size,
      entryPrice: executionPrice,
      orderExecutionType: 'market', // Default to market for backward compatibility
      stopLoss: orderData.stopLoss,
      takeProfit: orderData.takeProfit,
      timestamp: currentTimestamp,
      status: 'filled',
      commission,
      // Add chart index for accurate marker positioning
      ...(session.currentIndex >= 0 && { chartIndex: session.currentIndex })
    } as Order & { chartIndex?: number };

    // Current market price for position tracking (opposite side for unrealized P&L)
    const currentMarketPrice = orderData.type === 'buy' ? currentBidAsk.bid : currentBidAsk.ask;

    const newPosition: Position = {
      id: `pos_${orderId}`,
      orderId,
      type: orderData.type,
      symbol: session.symbol,
      size: orderData.size,
      entryPrice: executionPrice,
      currentPrice: currentMarketPrice,
      stopLoss: orderData.stopLoss,
      takeProfit: orderData.takeProfit,
      entryTimestamp: currentTimestamp,
      unrealizedPnL: 0, // Will be calculated properly after position is created
      commission
    };

    // Fix the unrealized P&L calculation
    newPosition.unrealizedPnL = calculateUnrealizedPnLBidAsk(newPosition, currentBidAsk);

    setSession(prev => ({
      ...prev,
      orders: [...prev.orders, newOrder],
      positions: [...prev.positions, newPosition]
    }));
  }, [session.data, session.currentIndex, session.symbol, session.account, getCurrentBidAsk]);

  const handleChartOrderPlace = useCallback((orderData: ChartOrderPlacement) => {
    // Check bounds for both candle and tick data
    const maxIndex = session.dataType === 'tick'
      ? (session.tickData?.length || 0)
      : session.data.length;

    if (maxIndex === 0 || session.currentIndex >= maxIndex) return;

    const currentCandle = session.data[session.currentIndex];
    const currentBidAsk = getCurrentBidAsk();

    if (!currentBidAsk) return;

    // For market orders, execute immediately
    if (orderData.orderExecutionType === 'market') {
      // Use appropriate bid/ask price for order execution
      const executionPrice = getExecutionPrice(orderData.orderType, currentBidAsk);

      // Check if order can be executed
      const canExecute = canExecuteOrder(session.account, orderData.orderType, orderData.size, executionPrice);
      if (!canExecute.canExecute) {
        alert(canExecute.reason);
        return;
      }

      const orderId = `order_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const commission = calculateCommission(orderData.size);

      // Get current timestamp (from candle or tick)
      const currentTimestamp = session.dataType === 'tick' && session.tickData
        ? session.tickData[session.currentIndex]?.timestamp || Date.now()
        : currentCandle?.timestamp || Date.now();

      const newOrder: Order = {
        id: orderId,
        type: orderData.orderType,
        symbol: session.symbol,
        size: orderData.size,
        entryPrice: executionPrice,
        orderExecutionType: orderData.orderExecutionType,
        stopLoss: orderData.stopLoss,
        takeProfit: orderData.takeProfit,
        timestamp: currentTimestamp,
        status: 'filled',
        commission,
        // Add chart index for accurate marker positioning
        ...(session.currentIndex >= 0 && { chartIndex: session.currentIndex })
      } as Order & { chartIndex?: number };

      // Current market price for position tracking (opposite side for unrealized P&L)
      const currentMarketPrice = orderData.orderType === 'buy' ? currentBidAsk.bid : currentBidAsk.ask;

      const newPosition: Position = {
        id: `pos_${orderId}`,
        orderId,
        type: orderData.orderType,
        symbol: session.symbol,
        size: orderData.size,
        entryPrice: executionPrice,
        currentPrice: currentMarketPrice,
        stopLoss: orderData.stopLoss,
        takeProfit: orderData.takeProfit,
        entryTimestamp: currentTimestamp,
        unrealizedPnL: 0, // Will be calculated properly after position is created
        commission
      };

      // Fix the unrealized P&L calculation
      newPosition.unrealizedPnL = calculateUnrealizedPnLBidAsk(newPosition, currentBidAsk);

      setSession(prev => ({
        ...prev,
        orders: [...prev.orders, newOrder],
        positions: [...prev.positions, newPosition]
      }));
    } else {
      // For pending orders, validate and create pending order
      const validation = validateOrderPrice(orderData.orderExecutionType, orderData.orderType, orderData.price, currentBidAsk);
      if (!validation.isValid) {
        alert(validation.error);
        return;
      }

      const orderId = `order_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Get current timestamp (from candle or tick)
      const currentTimestamp = session.dataType === 'tick' && session.tickData
        ? session.tickData[session.currentIndex]?.timestamp || Date.now()
        : currentCandle?.timestamp || Date.now();

      const newOrder: Order = {
        id: orderId,
        type: orderData.orderType,
        symbol: session.symbol,
        size: orderData.size,
        entryPrice: orderData.price, // For pending orders, this is the trigger price
        executionPrice: orderData.price, // The price at which the order should execute
        orderExecutionType: orderData.orderExecutionType,
        stopLoss: orderData.stopLoss,
        takeProfit: orderData.takeProfit,
        timestamp: currentTimestamp,
        status: 'pending',
        commission: 0, // Commission will be applied when order is filled
        // Add chart index for accurate marker positioning
        ...(session.currentIndex >= 0 && { chartIndex: session.currentIndex })
      } as Order & { chartIndex?: number };

      setSession(prev => ({
        ...prev,
        orders: [...prev.orders, newOrder]
      }));
    }
  }, [session.data, session.currentIndex, session.symbol, session.account, getCurrentBidAsk, validateOrderPrice, getExecutionPrice, canExecuteOrder, calculateCommission, calculateUnrealizedPnLBidAsk]);

  const togglePlayback = () => {
    setSession(prev => ({ ...prev, isPlaying: !prev.isPlaying }));
  };

  const stepForward = () => {
    if (session.dataType === 'tick') {
      if (session.updateMode === 'complete') {
        // Tick mode complete: advance to next tick
        const maxIndex = (session.tickData?.length || 0) - 1;
        setSession(prev => ({
          ...prev,
          currentIndex: Math.min(prev.currentIndex + 1, maxIndex),
          isPlaying: false
        }));
      } else {
        // Tick mode intra-candle: advance step within current candle or move to next candle
        if (intraCandleStep < intraCandleSteps - 1) {
          setIntraCandleStep(intraCandleStep + 1);
        } else {
          // Move to next candle and reset step
          setSession(prev => ({
            ...prev,
            currentIndex: Math.min(prev.currentIndex + 1, prev.data.length - 1),
            isPlaying: false
          }));
          setIntraCandleStep(0);
        }
      }
    } else if (session.updateMode === 'complete' || session.timeframe === 'M1') {
      // Standard mode: advance to next candle
      setSession(prev => ({
        ...prev,
        currentIndex: Math.min(prev.currentIndex + 1, prev.data.length - 1),
        isPlaying: false
      }));
    } else {
      // Intra-candle mode: advance step within current candle or move to next candle
      if (intraCandleStep < intraCandleSteps - 1) {
        setIntraCandleStep(intraCandleStep + 1);
      } else {
        // Move to next candle and reset step
        setSession(prev => ({
          ...prev,
          currentIndex: Math.min(prev.currentIndex + 1, prev.data.length - 1),
          isPlaying: false
        }));
        setIntraCandleStep(0);
      }
    }
  };

  const stepBackward = () => {
    if (session.dataType === 'tick') {
      if (session.updateMode === 'complete') {
        // Tick mode complete: go back to previous tick
        setSession(prev => ({
          ...prev,
          currentIndex: Math.max(prev.currentIndex - 1, 0),
          isPlaying: false
        }));
      } else {
        // Tick mode intra-candle: go back one step
        if (intraCandleStep > 0) {
          setIntraCandleStep(intraCandleStep - 1);
        } else {
          // Move to previous candle and set to last step
          if (session.currentIndex > 0) {
            setSession(prev => ({
              ...prev,
              currentIndex: Math.max(prev.currentIndex - 1, 0),
              isPlaying: false
            }));
            setIntraCandleStep(intraCandleSteps - 1);
          }
        }
      }
    } else if (session.updateMode === 'complete' || session.timeframe === 'M1') {
      // Standard mode: go back to previous candle
      setSession(prev => ({
        ...prev,
        currentIndex: Math.max(prev.currentIndex - 1, 0),
        isPlaying: false
      }));
    } else {
      // Intra-candle mode: go back one step
      if (intraCandleStep > 0) {
        setIntraCandleStep(intraCandleStep - 1);
      } else {
        // Move to previous candle and set to last step
        if (session.currentIndex > 0) {
          setSession(prev => ({
            ...prev,
            currentIndex: Math.max(prev.currentIndex - 1, 0),
            isPlaying: false
          }));
          setIntraCandleStep(intraCandleSteps - 1);
        }
      }
    }
  };

  const resetSession = () => {
    setSession(prev => {
      // Get initial bid/ask from first data point
      let initialBid = 0;
      let initialAsk = 0;
      let initialSpread = 0;

      if (prev.dataType === 'tick' && prev.tickData && prev.tickData.length > 0) {
        const firstTick = prev.tickData[0];
        const bidAsk = getBidAskFromTick(firstTick, 0, 0); // No fallback for first tick
        if (bidAsk) {
          initialBid = bidAsk.bid;
          initialAsk = bidAsk.ask;
          initialSpread = bidAsk.spread;
        }
      } else if (prev.data.length > 0) {
        const firstCandle = prev.data[0];
        // For tick-derived candles treat close as bid, for regular candles treat as mid
        const treatCloseAsBid = prev.dataType === 'tick';
        const bidAsk = getBidAskFromCandle(firstCandle, undefined, treatCloseAsBid);
        initialBid = bidAsk.bid;
        initialAsk = bidAsk.ask;
        initialSpread = bidAsk.spread;
      }

      return {
        ...prev,
        currentIndex: 0,
        isPlaying: false,
        orders: [],
        positions: [],
        account: {
          balance: INITIAL_BALANCE,
          equity: INITIAL_BALANCE,
          margin: 0,
          freeMargin: INITIAL_BALANCE,
          marginLevel: 0,
          totalPnL: 0,
          totalCommission: 0
        },
        startBalance: INITIAL_BALANCE,
        currentBid: initialBid,
        currentAsk: initialAsk,
        spread: initialSpread,
        lastKnownBid: initialBid,
        lastKnownAsk: initialAsk
      };
    });
    setIntraCandleStep(0);
  };

  const handleTimeframeChange = useCallback((newTimeframe: Timeframe) => {
    setSession(prev => {
      let aggregatedData: CandleData[] = [];

      if (prev.dataType === 'tick' && prev.tickData) {
        // For tick data, convert to new timeframe candles
        const timeframeSeconds = getTimeframeSeconds(newTimeframe);
        aggregatedData = convertTicksToCandles(prev.tickData, timeframeSeconds / 60); // Convert to minutes for existing function
      } else if (prev.baseData.length > 0) {
        // For candle data, use aggregator
        const aggregator = new TimeframeAggregator(prev.baseData);
        aggregatedData = aggregator.aggregate(newTimeframe);
      } else {
        return prev; // No data to work with
      }

      // Find corresponding index in new timeframe
      let newIndex = 0;
      if (prev.currentIndex < prev.data.length && prev.data[prev.currentIndex]) {
        const currentTimestamp = prev.data[prev.currentIndex].timestamp;

        // Find closest timestamp in aggregated data
        for (let i = 0; i < aggregatedData.length; i++) {
          if (aggregatedData[i].timestamp <= currentTimestamp) {
            newIndex = i;
          } else {
            break;
          }
        }
      }

      return {
        ...prev,
        data: aggregatedData,
        currentIndex: Math.min(newIndex, aggregatedData.length - 1),
        timeframe: newTimeframe,
        isPlaying: false // Pause when switching timeframes
      };
    });
  }, [session.baseData, session.currentIndex, session.data, session.dataType, session.tickData, getTimeframeSeconds, convertTicksToCandles]);

  const handleUpdateModeChange = useCallback((newMode: UpdateMode) => {
    setSession(prev => ({
      ...prev,
      updateMode: newMode,
      isPlaying: false // Pause when switching update modes
    }));
    setIntraCandleStep(0);
  }, []);



  // Update intra-candle steps when timeframe or update mode changes
  useEffect(() => {
    if (session.updateMode === 'complete') {
      setIntraCandleSteps(1);
    } else if (session.dataType === 'tick') {
      // For tick data, use number of ticks in current candle
      if (session.currentIndex >= session.data.length || !session.tickData) {
        setIntraCandleSteps(1);
        setIntraCandleStep(0);
        return;
      }

      const currentCandle = session.data[session.currentIndex];
      if (!currentCandle) {
        setIntraCandleSteps(1);
        setIntraCandleStep(0);
        return;
      }

      // Find all ticks within this candle's time range
      const ticksInCandle = getTicksInCandle(session.tickData, currentCandle, session.timeframe);

      setIntraCandleSteps(Math.max(1, ticksInCandle.length));
    } else {
      // For candle data, use timeframe seconds converted to steps
      const timeframeSeconds = getTimeframeSeconds(session.timeframe);
      // For candle data, use minutes as steps (legacy behavior)
      const steps = timeframeSeconds >= 60 ? Math.floor(timeframeSeconds / 60) : 1;
      setIntraCandleSteps(steps);
    }
    setIntraCandleStep(0);
  }, [session.timeframe, session.updateMode, session.dataType, session.currentIndex, session.data, session.tickData, getTicksInCandle]);

  if (showImporter || (session.data.length === 0 && !session.tickData?.length)) {
    return (
      <div className="min-h-screen bg-trading-surface">
        <TradingHeader
          symbol=""
          totalCandles={0}
          currentBalance={session.account.balance}
          totalPnL={session.account.totalPnL}
          onImportData={() => {}}
          isConnected={false}
        />

        <div className="max-w-4xl mx-auto px-4 py-12">
          <div className="text-center mb-12 animate-fade-in">
            <div className="mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-trading-info to-trading-accent rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Play className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-trading-text-primary mb-3">
                Welcome to FX Backtester
              </h1>
              <p className="text-xl text-trading-text-secondary max-w-2xl mx-auto">
                Professional forex backtesting platform with advanced analytics and MetaTrader integration
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-3xl mx-auto">
              <div className="trading-panel p-6 text-center">
                <div className="w-12 h-12 bg-trading-info/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <SkipForward className="h-6 w-6 text-trading-info" />
                </div>
                <h3 className="font-semibold text-trading-text-primary mb-2">CSV Import</h3>
                <p className="text-sm text-trading-text-secondary">
                  Import MetaTrader CSV data with automatic validation
                </p>
              </div>

              <div className="trading-panel p-6 text-center">
                <div className="w-12 h-12 bg-trading-success/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <RotateCcw className="h-6 w-6 text-trading-success" />
                </div>
                <h3 className="font-semibold text-trading-text-primary mb-2">Live Charts</h3>
                <p className="text-sm text-trading-text-secondary">
                  Interactive candlestick charts with real-time updates
                </p>
              </div>

              <div className="trading-panel p-6 text-center">
                <div className="w-12 h-12 bg-trading-warning/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Settings className="h-6 w-6 text-trading-warning" />
                </div>
                <h3 className="font-semibold text-trading-text-primary mb-2">Analytics</h3>
                <p className="text-sm text-trading-text-secondary">
                  Comprehensive performance metrics and reporting
                </p>
              </div>
            </div>
          </div>

          <div className="animate-slide-up">
            <CSVImporter onDataLoaded={handleDataLoaded} />
          </div>

          {(session.data.length > 0 || session.tickData?.length) && (
            <div className="mt-8 text-center animate-fade-in">
              <button
                onClick={() => setShowImporter(false)}
                className="trading-button-primary px-8 py-3 text-lg"
              >
                Start Trading Session
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-trading-surface">
      <TradingHeader
        symbol={session.symbol}
        totalCandles={totalItems}
        currentBalance={session.account.balance}
        totalPnL={session.account.totalPnL}
        onImportData={() => setShowImporter(true)}
        isConnected={true}
        currentBid={session.currentBid}
        currentAsk={session.currentAsk}
        spread={session.spread}
        precision={session.precision}
      />

      {/* Main content */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Chart area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Playback controls */}
            <div className="trading-card">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={stepBackward}
                    disabled={session.currentIndex === 0}
                    className="p-3 rounded-lg bg-trading-surface border border-trading-border hover:bg-trading-accent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 text-trading-text-primary"
                  >
                    <SkipBack className="h-4 w-4" />
                  </button>

                  <button
                    onClick={togglePlayback}
                    disabled={session.currentIndex >= totalItems - 1}
                    className={`p-3 rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ${
                      session.isPlaying
                        ? 'bg-trading-danger hover:bg-red-600 animate-pulse'
                        : 'trading-button-primary'
                    }`}
                  >
                    {session.isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </button>

                  <button
                    onClick={stepForward}
                    disabled={session.currentIndex >= totalItems - 1}
                    className="p-3 rounded-lg bg-trading-surface border border-trading-border hover:bg-trading-accent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 text-trading-text-primary"
                  >
                    <SkipForward className="h-4 w-4" />
                  </button>

                  <button
                    onClick={resetSession}
                    className="p-3 rounded-lg bg-trading-danger/20 hover:bg-trading-danger/30 text-trading-danger border border-trading-danger/30 transition-all duration-200"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </button>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="text-sm text-trading-text-secondary">
                    Speed: <span className="text-trading-text-primary font-medium">
                      {session.playbackSpeed === 'realtime' ? 'Realtime' : `${session.playbackSpeed}ms`}
                    </span>
                  </div>
                  <select
                    value={session.playbackSpeed}
                    onChange={(e) => {
                      const value = e.target.value;
                      setSession(prev => ({
                        ...prev,
                        playbackSpeed: value === 'realtime' ? 'realtime' : parseInt(value)
                      }));
                    }}
                    className="trading-input text-sm py-1"
                  >
                    <option value={100}>Fast (100ms)</option>
                    <option value={500}>Medium (500ms)</option>
                    <option value={1000}>Normal (1s)</option>
                    <option value={2000}>Slow (2s)</option>
                    {session.dataType === 'tick' && (
                      <option value="realtime">Realtime (Actual Tick Timing)</option>
                    )}
                  </select>
                </div>
              </div>

              {/* Progress bar */}
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-trading-text-secondary">
                    {session.dataType === 'tick' ? 'Tick' : 'Candle'} {session.currentIndex + 1} of {totalItems}
                  </span>
                  <span className="text-trading-text-primary font-medium">
                    {progress.toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-trading-surface rounded-full h-3 border border-trading-border">
                  <div
                    className="bg-gradient-to-r from-trading-info to-trading-accent h-3 rounded-full transition-all duration-300 shadow-sm"
                    style={{ width: `${progress}%` }}
                  />
                </div>

                {/* Current data info */}
                {session.dataType === 'tick' && currentTick ? (
                  <div className="flex items-center justify-between text-xs text-trading-text-secondary pt-2 border-t border-trading-border">
                    <span>{currentTick.date} {currentTick.time}</span>
                    <div className="flex items-center space-x-4">
                      {currentTick.bid !== undefined && (
                        <span>Bid: <span className="text-trading-danger">{formatPrice(currentTick.bid)}</span></span>
                      )}
                      {currentTick.ask !== undefined && (
                        <span>Ask: <span className="text-trading-success">{formatPrice(currentTick.ask)}</span></span>
                      )}
                      {currentTick.last !== undefined && (
                        <span>Last: <span className="text-trading-text-primary">{formatPrice(currentTick.last)}</span></span>
                      )}
                      <span>Flags: <span className="text-trading-info">{currentTick.flags}</span></span>
                    </div>
                  </div>
                ) : currentCandle && (
                  <div className="flex items-center justify-between text-xs text-trading-text-secondary pt-2 border-t border-trading-border">
                    <span>{currentCandle.date} {currentCandle.time}</span>
                    <div className="flex items-center space-x-4">
                      <span>O: <span className="text-trading-text-primary">{formatPrice(currentCandle.open)}</span></span>
                      <span>H: <span className="text-trading-text-primary">{formatPrice(currentCandle.high)}</span></span>
                      <span>L: <span className="text-trading-text-primary">{formatPrice(currentCandle.low)}</span></span>
                      <span>C: <span className="text-trading-text-primary">{formatPrice(currentCandle.close)}</span></span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Timeframe and Update Mode Selectors */}
            <div className="trading-panel p-4 space-y-4">
              <TimeframeSelector
                currentTimeframe={session.timeframe}
                onTimeframeChange={handleTimeframeChange}
                disabled={session.isPlaying}
              />

              {/* Update Mode Selector */}
              <UpdateModeSelector
                currentMode={session.updateMode}
                currentTimeframe={session.timeframe}
                onModeChange={handleUpdateModeChange}
                disabled={session.isPlaying}
                dataType={session.dataType}
                intraCandleProgress={{
                  step: intraCandleStep + 1,
                  total: intraCandleSteps,
                  progress: intraCandleSteps > 1 ? intraCandleStep / (intraCandleSteps - 1) : 1
                }}
              />
            </div>

            {/* Tick data info panel */}
            {session.dataType === 'tick' && (
              <div className="trading-panel p-4">
                <h3 className="text-sm font-medium text-trading-text-primary mb-3">
                  Tick-to-Candle Mode
                </h3>
                <div className="text-xs text-trading-text-secondary space-y-2">
                  <div className="flex justify-between">
                    <span>Data Source:</span>
                    <span className="text-trading-success">Tick Data</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Chart Display:</span>
                    <span className="text-trading-info">{session.timeframe} Candles</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Update Mode:</span>
                    <span className="text-trading-warning">{session.updateMode === 'complete' ? 'Complete' : 'Intra-Candle'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Ticks:</span>
                    <span className="text-trading-text-primary">{session.tickData?.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Generated Candles:</span>
                    <span className="text-trading-text-primary">{session.data.length}</span>
                  </div>
                  {session.updateMode === 'intraCandle' && (
                    <div className="flex justify-between">
                      <span>Current Candle Ticks:</span>
                      <span className="text-trading-accent">{intraCandleSteps}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span>Tick Speed:</span>
                    <span className="text-trading-text-primary">
                      {session.playbackSpeed === 'realtime' ? 'Realtime' : `${session.playbackSpeed}ms`}
                    </span>
                  </div>
                  {session.playbackSpeed === 'realtime' && (
                    <div className="flex justify-between">
                      <span>Mode:</span>
                      <span className="text-trading-success">Actual Tick Timing</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Chart */}
            <TradingChart
              data={displayData}
              baseData={session.baseData}
              currentIndex={session.currentIndex}
              positions={session.positions}
              orders={session.orders}
              onChartOrderPlace={handleChartOrderPlace}
              height={500}
              timeframe={session.timeframe}
              updateMode={session.updateMode}
              intraCandleStep={intraCandleStep}
              dataType={session.dataType}
              precision={session.precision}
              currentBid={session.currentBid}
              currentAsk={session.currentAsk}
              spread={session.spread}
            />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order panel */}
            <OrderPanel
              currentPrice={getCurrentStepPrice()}
              currentBid={session.currentBid}
              currentAsk={session.currentAsk}
              spread={session.spread}
              onPlaceOrder={handlePlaceOrder}
              canTrade={session.currentIndex < totalItems}
              balance={session.account.balance}
              freeMargin={session.account.freeMargin}
              precision={session.precision}
            />

            {/* Performance panel */}
            <PerformancePanel
              orders={session.orders}
              startBalance={session.startBalance}
              currentBalance={session.account.balance}
            />

            {/* Positions */}
            {session.positions.length > 0 && (
              <div className="trading-card">
                <h3 className="text-lg font-semibold text-trading-text-primary mb-4 flex items-center">
                  <div className="w-2 h-2 bg-trading-success rounded-full mr-2 animate-pulse"></div>
                  Open Positions
                </h3>
                <div className="space-y-3">
                  {session.positions.map(position => (
                    <div key={position.id} className="p-4 bg-trading-surface border border-trading-border rounded-lg hover:border-trading-info/50 transition-colors">
                      <div className="flex justify-between items-start mb-3">
                        <div className="text-sm font-medium text-trading-text-primary">
                          <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                            position.type === 'buy' ? 'bg-trading-success' : 'bg-trading-danger'
                          }`}></span>
                          {position.type.toUpperCase()} {position.size} lots
                        </div>
                        <div className={`text-sm font-bold ${position.unrealizedPnL >= 0 ? 'stat-positive' : 'stat-negative'}`}>
                          {formatCurrency(position.unrealizedPnL)}
                        </div>
                      </div>
                      <div className="text-xs text-trading-text-secondary space-y-1 mb-3">
                        <div className="flex justify-between">
                          <span>Entry:</span>
                          <span className="text-trading-text-primary font-mono">{formatPrice(position.entryPrice)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Current:</span>
                          <span className="text-trading-text-primary font-mono">{formatPrice(position.currentPrice)}</span>
                        </div>
                        {position.stopLoss && (
                          <div className="flex justify-between">
                            <span>Stop Loss:</span>
                            <span className="text-trading-danger font-mono">{formatPrice(position.stopLoss)}</span>
                          </div>
                        )}
                        {position.takeProfit && (
                          <div className="flex justify-between">
                            <span>Take Profit:</span>
                            <span className="text-trading-success font-mono">{formatPrice(position.takeProfit)}</span>
                          </div>
                        )}
                      </div>
                      <button
                        onClick={() => handleClosePosition(position.id)}
                        className="w-full trading-button-danger text-sm py-2"
                      >
                        Close Position
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recent orders */}
            {session.orders.length > 0 && (
              <div className="trading-card">
                <h3 className="text-lg font-semibold text-trading-text-primary mb-4 flex items-center">
                  <div className="w-2 h-2 bg-trading-info rounded-full mr-2"></div>
                  Order History
                  <span className="ml-2 text-xs bg-trading-info/20 text-trading-info px-2 py-1 rounded-full">
                    {session.orders.length}
                  </span>
                </h3>
                <div className="space-y-2 max-h-80 overflow-y-auto custom-scrollbar">
                  {session.orders.slice(-15).reverse().map(order => (
                    <div key={order.id} className="p-3 bg-trading-surface border border-trading-border rounded-lg hover:border-trading-info/30 transition-colors">
                      <div className="flex justify-between items-center mb-2">
                        <span className={`text-sm font-medium flex items-center ${
                          order.type === 'buy' ? 'text-trading-success' : 'text-trading-danger'
                        }`}>
                          <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                            order.type === 'buy' ? 'bg-trading-success' : 'bg-trading-danger'
                          }`}></span>
                          {order.type.toUpperCase()} {order.size}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          order.status === 'closed'
                            ? 'bg-trading-success/20 text-trading-success'
                            : order.status === 'open'
                            ? 'bg-trading-warning/20 text-trading-warning'
                            : 'bg-trading-text-muted/20 text-trading-text-muted'
                        }`}>
                          {order.status}
                        </span>
                      </div>
                      <div className="text-xs text-trading-text-secondary space-y-1">
                        <div className="flex justify-between">
                          <span>Entry:</span>
                          <span className="text-trading-text-primary font-mono">{formatPrice(order.entryPrice)}</span>
                        </div>
                        {order.exitPrice && (
                          <div className="flex justify-between">
                            <span>Exit:</span>
                            <span className="text-trading-text-primary font-mono">{formatPrice(order.exitPrice)}</span>
                          </div>
                        )}
                      </div>
                      {order.pnl !== undefined && (
                        <div className={`text-sm font-bold mt-2 ${order.pnl >= 0 ? 'stat-positive' : 'stat-negative'}`}>
                          {formatCurrency(order.pnl)}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
